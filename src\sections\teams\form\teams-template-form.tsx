import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Step,
  StepLabel,
  TextField,
  Button,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  Breadcrumbs,
  Link,
  Paper,
  StepConnector,
  Card,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { Icon } from '@iconify/react';
import { useRouter } from 'src/routes/hooks';
import { AppButton, AppContainer } from 'src/components/common';

// Custom styled components to match the design
const CustomStepConnector = styled(StepConnector)(({ theme }) => ({
  '&.MuiStepConnector-alternativeLabel': {
    top: 22,
    left: 'calc(-50% + 16px)',
    right: 'calc(50% + 16px)',
  },
  '& .MuiStepConnector-line': {
    height: 3,
    border: 0,
    backgroundColor: '#E5E7EB',
    borderRadius: 1,
  },
  '&.MuiStepConnector-active .MuiStepConnector-line': {
    backgroundColor: '#8B5CF6',
  },
  '&.MuiStepConnector-completed .MuiStepConnector-line': {
    backgroundColor: '#8B5CF6',
  },
}));

const CustomStepIcon = styled('div')<{ active?: boolean; completed?: boolean }>(
  ({ active, completed }) => ({
    width: 44,
    height: 44,
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '1rem',
    fontWeight: 600,
    backgroundColor: active || completed ? '#8B5CF6' : '#F3F4F6',
    color: active || completed ? '#FFFFFF' : '#9CA3AF',
    border: active || completed ? 'none' : '2px solid #E5E7EB',
    transition: 'all 0.2s ease-in-out',
    zIndex: 1,
  })
);

const TeamsTemplateForm = () => {
  const router = useRouter();
  const [activeStep, setActiveStep] = useState(0);
  const [teamName, setTeamName] = useState('');
  const [description, setDescription] = useState('');
  const [flowControl, setFlowControl] = useState('auto');

  const steps = [
    'Team Information',
    'Tools & Members',
    'Configuration',
    'Instructions',
    'Frequency',
  ];

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleCancel = () => {
    router.push('/dashboard/teams');
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Typography variant="h5" fontWeight={700} mb={1} color="#1F2937">
              Team Information
            </Typography>
            <Typography variant="body2" color="#6B7280" mb={1} fontSize="0.875rem">
              Create your own team and view your team members and tools
            </Typography>
            <Card
              sx={{
                background: 'white',
                padding: '20px',
                display: 'flex',
                flexDirection: 'column',
                gap: '16px',
              }}
            >
              <Box sx={{}}>
                <Typography mb="8px" variant="body2" fontWeight={500} color="#374151">
                  Team Name
                </Typography>
                <TextField
                  fullWidth
                  placeholder="Type your team name"
                  value={teamName}
                  onChange={(e) => setTeamName(e.target.value)}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      bgcolor: '#FFFFFF',
                      borderRadius: '8px',
                      height: '48px',
                      '& fieldset': {
                        borderColor: '#D1D5DB',
                        borderWidth: '1px',
                      },
                      '&:hover fieldset': {
                        borderColor: '#9CA3AF',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#8B5CF6',
                        borderWidth: '2px',
                      },
                    },
                    '& .MuiInputBase-input': {
                      fontSize: '0.875rem',
                      color: '#374151',
                      '&::placeholder': {
                        color: '#9CA3AF',
                        opacity: 1,
                      },
                    },
                  }}
                />
              </Box>

              <Box>
                <Typography mb="8px" variant="body2" fontWeight={500} color="#374151">
                  Description
                </Typography>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  placeholder="Type your team description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      bgcolor: '#FFFFFF',
                      borderRadius: '8px',
                      '& fieldset': {
                        borderColor: '#D1D5DB',
                        borderWidth: '1px',
                      },
                      '&:hover fieldset': {
                        borderColor: '#9CA3AF',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#8B5CF6',
                        borderWidth: '2px',
                      },
                    },
                    '& .MuiInputBase-input': {
                      fontSize: '0.875rem',
                      color: '#374151',
                      '&::placeholder': {
                        color: '#9CA3AF',
                        opacity: 1,
                      },
                    },
                  }}
                />
              </Box>

              <Box>
                <Typography variant="body2" fontWeight={500} color="#374151">
                  Flow Control
                </Typography>
                <FormControl>
                  <RadioGroup
                    row
                    value={flowControl}
                    onChange={(e) => setFlowControl(e.target.value)}
                    sx={{ gap: 4 }}
                  >
                    <FormControlLabel
                      value="auto"
                      control={
                        <Radio
                          sx={{
                            color: '#D1D5DB',
                            '&.Mui-checked': {
                              color: '#8B5CF6',
                            },
                            '& .MuiSvgIcon-root': {
                              fontSize: 20,
                            },
                          }}
                        />
                      }
                      label={
                        <Typography
                          variant="body2"
                          sx={{ fontSize: '0.875rem', color: '#374151', fontWeight: 500 }}
                        >
                          Auto
                        </Typography>
                      }
                    />
                    <FormControlLabel
                      value="manual"
                      control={
                        <Radio
                          sx={{
                            color: '#D1D5DB',
                            '&.Mui-checked': {
                              color: '#8B5CF6',
                            },
                            '& .MuiSvgIcon-root': {
                              fontSize: 20,
                            },
                          }}
                        />
                      }
                      label={
                        <Typography
                          variant="body2"
                          sx={{ fontSize: '0.875rem', color: '#374151', fontWeight: 500 }}
                        >
                          Manual
                        </Typography>
                      }
                    />
                  </RadioGroup>
                </FormControl>
              </Box>
            </Card>
          </Box>
        );
      default:
        return (
          <Box textAlign="center" py={4}>
            <Typography variant="h6" color="text.secondary">
              Step {step + 1} content will be implemented here
            </Typography>
          </Box>
        );
    }
  };

  return (
    <AppContainer title="Create Team" routeLinks={[{ name: 'Teams' }, { name: 'Create Team' }]}>
      {/* Stepper */}
      <hr />
      <Stepper
        activeStep={activeStep}
        alternativeLabel
        connector={<CustomStepConnector />}
        sx={{
          mt: '24px',
          mb: 6,
          '& .MuiStepLabel-root': {
            padding: 0,
          },
          '& .MuiStepLabel-labelContainer': {
            marginTop: 2,
          },
        }}
      >
        {steps.map((label, index) => (
          <Step key={label}>
            <StepLabel
              StepIconComponent={({ active, completed }) => (
                <CustomStepIcon active={active} completed={completed}>
                  {completed ? <Icon icon="eva:checkmark-fill" width={20} /> : index + 1}
                </CustomStepIcon>
              )}
              sx={{
                '& .MuiStepLabel-label': {
                  fontWeight: activeStep === index ? 600 : 500,
                  color: activeStep === index ? '#1F2937' : '#6B7280',
                  fontSize: '0.875rem',
                  marginTop: '12px',
                  textAlign: 'center',
                },
                '& .MuiStepLabel-label.Mui-active': {
                  color: '#1F2937',
                },
                '& .MuiStepLabel-label.Mui-completed': {
                  color: '#6B7280',
                },
              }}
            >
              {label}
            </StepLabel>
          </Step>
        ))}
      </Stepper>

      {/* Step Content */}
      <Box
        sx={{
          background: (theme) => theme.palette.background.neutral,
          padding: '20px',
          borderRadius: '16px',
        }}
      >
        <Box sx={{ mb: '16px' }}>{renderStepContent(activeStep)}</Box>

        {/* Action Buttons */}
        <Box display="flex" gap={3}>
          <AppButton
            size="small"
            variant="outlined"
            onClick={handleCancel}
            sx={{
              flex: 1,
              py: 2,
              borderColor: '#D1D5DB',
              color: '#8B5CF6',
              fontSize: '0.875rem',
              fontWeight: 600,
              textTransform: 'none',
              borderRadius: '8px',

              '&:hover': {
                borderColor: '#8B5CF6',
                bgcolor: 'rgba(139, 92, 246, 0.04)',
              },
            }}
            label="Cancel"
          />

          <AppButton
            size="small"
            variant="contained"
            onClick={activeStep === steps.length - 1 ? handleCancel : handleNext}
            sx={{
              flex: 1,
              py: 2,
              bgcolor: '#8B5CF6',
              fontSize: '0.875rem',
              fontWeight: 600,
              textTransform: 'none',
              borderRadius: '8px',

              boxShadow: 'none',
              '&:hover': {
                bgcolor: '#7C3AED',
                boxShadow: 'none',
              },
            }}
            label={activeStep === steps.length - 1 ? 'Create Team' : 'Next'}
          />
        </Box>
      </Box>
    </AppContainer>
  );
};

export default TeamsTemplateForm;
