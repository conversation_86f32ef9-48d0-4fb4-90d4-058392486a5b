import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>per,
  Step,
  StepLabel,
  TextField,
  Button,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  Breadcrumbs,
  Link,
  Paper,
  StepConnector,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { Icon } from '@iconify/react';
import { useRouter } from 'src/routes/hooks';

// Custom styled components
const CustomStepConnector = styled(StepConnector)(({ theme }) => ({
  '&.MuiStepConnector-alternativeLabel': {
    top: 16,
  },
  '& .MuiStepConnector-line': {
    height: 2,
    border: 0,
    backgroundColor: '#E5E7EB',
    borderRadius: 1,
  },
  '&.MuiStepConnector-active .MuiStepConnector-line': {
    backgroundColor: '#7C3AED',
  },
  '&.MuiStepConnector-completed .MuiStepConnector-line': {
    backgroundColor: '#7C3AED',
  },
}));

const CustomStepIcon = styled('div')<{ active?: boolean; completed?: boolean }>(
  ({ theme, active, completed }) => ({
    width: 32,
    height: 32,
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '0.875rem',
    fontWeight: 600,
    backgroundColor: active || completed ? '#7C3AED' : '#E5E7EB',
    color: active || completed ? '#FFFFFF' : '#6B7280',
    transition: 'all 0.2s ease-in-out',
  })
);

const TeamsTemplateForm = () => {
  const router = useRouter();
  const [activeStep, setActiveStep] = useState(0);
  const [teamName, setTeamName] = useState('');
  const [description, setDescription] = useState('');
  const [flowControl, setFlowControl] = useState('auto');

  const steps = [
    'Team Information',
    'Tools & Members',
    'Configuration',
    'Instructions',
    'Frequency',
  ];

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleCancel = () => {
    router.push('/dashboard/teams');
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Typography variant="h5" fontWeight={600} mb={1}>
              Team Information
            </Typography>
            <Typography variant="body2" color="text.secondary" mb={4}>
              Create your own team and view your team members and tools
            </Typography>

            <Box mb={3}>
              <Typography variant="body2" fontWeight={500} mb={1.5} color="text.primary">
                Team Name
              </Typography>
              <TextField
                fullWidth
                placeholder="Type your team name"
                value={teamName}
                onChange={(e) => setTeamName(e.target.value)}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    bgcolor: '#F9FAFB',
                    borderRadius: 1,
                    '& fieldset': {
                      borderColor: '#E5E7EB',
                    },
                    '&:hover fieldset': {
                      borderColor: '#D1D5DB',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#7C3AED',
                    },
                  },
                  '& .MuiInputBase-input': {
                    fontSize: '0.875rem',
                  },
                }}
              />
            </Box>

            <Box mb={3}>
              <Typography variant="body2" fontWeight={500} mb={1.5} color="text.primary">
                Description
              </Typography>
              <TextField
                fullWidth
                multiline
                rows={3}
                placeholder="Type your team description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    bgcolor: '#F9FAFB',
                    borderRadius: 1,
                    '& fieldset': {
                      borderColor: '#E5E7EB',
                    },
                    '&:hover fieldset': {
                      borderColor: '#D1D5DB',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#7C3AED',
                    },
                  },
                  '& .MuiInputBase-input': {
                    fontSize: '0.875rem',
                  },
                }}
              />
            </Box>

            <Box mb={4}>
              <Typography variant="body2" fontWeight={500} mb={2} color="text.primary">
                Flow Control
              </Typography>
              <FormControl>
                <RadioGroup
                  row
                  value={flowControl}
                  onChange={(e) => setFlowControl(e.target.value)}
                >
                  <FormControlLabel
                    value="auto"
                    control={
                      <Radio
                        size="small"
                        sx={{
                          color: '#D1D5DB',
                          '&.Mui-checked': {
                            color: '#7C3AED',
                          },
                        }}
                      />
                    }
                    label={
                      <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                        Auto
                      </Typography>
                    }
                    sx={{ mr: 4 }}
                  />
                  <FormControlLabel
                    value="manual"
                    control={
                      <Radio
                        size="small"
                        sx={{
                          color: '#D1D5DB',
                          '&.Mui-checked': {
                            color: '#7C3AED',
                          },
                        }}
                      />
                    }
                    label={
                      <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                        Manual
                      </Typography>
                    }
                  />
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>
        );
      default:
        return (
          <Box textAlign="center" py={4}>
            <Typography variant="h6" color="text.secondary">
              Step {step + 1} content will be implemented here
            </Typography>
          </Box>
        );
    }
  };

  return (
    <Box sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link
          color="text.secondary"
          onClick={() => router.push('/dashboard/teams')}
          sx={{ cursor: 'pointer', textDecoration: 'none' }}
        >
          Teams
        </Link>
        <Typography color="text.primary">Create Team</Typography>
      </Breadcrumbs>

      {/* Main Content Card */}
      <Paper
        elevation={0}
        sx={{
          p: 4,
          borderRadius: 2,
          border: '1px solid',
          borderColor: 'divider',
          bgcolor: 'background.paper',
        }}
      >
        {/* Header */}
        <Typography variant="h4" fontWeight={700} mb={4}>
          Create Team
        </Typography>

        {/* Stepper */}
        <Stepper
          activeStep={activeStep}
          connector={<CustomStepConnector />}
          sx={{
            mb: 4,
            '& .MuiStepLabel-root': {
              padding: '0 8px',
            },
          }}
        >
          {steps.map((label, index) => (
            <Step key={label}>
              <StepLabel
                StepIconComponent={({ active, completed }) => (
                  <CustomStepIcon active={active} completed={completed}>
                    {completed ? <Icon icon="eva:checkmark-fill" width={16} /> : index + 1}
                  </CustomStepIcon>
                )}
                sx={{
                  '& .MuiStepLabel-label': {
                    fontWeight: activeStep === index ? 600 : 400,
                    color: activeStep === index ? 'text.primary' : 'text.secondary',
                    fontSize: '0.875rem',
                    marginTop: '8px',
                  },
                }}
              >
                {label}
              </StepLabel>
            </Step>
          ))}
        </Stepper>

        {/* Step Content */}
        <Box sx={{ mb: 4 }}>{renderStepContent(activeStep)}</Box>

        {/* Action Buttons */}
        <Box display="flex" gap={2} mt={4}>
          <Button
            variant="outlined"
            onClick={handleCancel}
            sx={{
              flex: 1,
              py: 1.5,
              borderColor: '#E5E7EB',
              color: '#6B7280',
              fontSize: '0.875rem',
              fontWeight: 500,
              textTransform: 'none',
              borderRadius: 1,
              '&:hover': {
                borderColor: '#D1D5DB',
                bgcolor: '#F9FAFB',
              },
            }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={activeStep === steps.length - 1 ? handleCancel : handleNext}
            sx={{
              flex: 1,
              py: 1.5,
              bgcolor: '#7C3AED',
              fontSize: '0.875rem',
              fontWeight: 500,
              textTransform: 'none',
              borderRadius: 1,
              boxShadow: 'none',
              '&:hover': {
                bgcolor: '#6D28D9',
                boxShadow: 'none',
              },
            }}
          >
            {activeStep === steps.length - 1 ? 'Create Team' : 'Next'}
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

export default TeamsTemplateForm;
