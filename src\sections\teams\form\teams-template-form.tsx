import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>per,
  Step,
  StepLabel,
  TextField,
  Button,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  Breadcrumbs,
  Link,
} from '@mui/material';
import { Icon } from '@iconify/react';
import { useRouter } from 'src/routes/hooks';

const TeamsTemplateForm = () => {
  const router = useRouter();
  const [activeStep, setActiveStep] = useState(0);
  const [teamName, setTeamName] = useState('');
  const [description, setDescription] = useState('');
  const [flowControl, setFlowControl] = useState('auto');

  const steps = [
    'Team Information',
    'Tools & Members',
    'Configuration',
    'Instructions',
    'Frequency',
  ];

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleCancel = () => {
    router.push('/dashboard/teams');
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Typography variant="h6" fontWeight={600} mb={1}>
              Team Information
            </Typography>
            <Typography variant="body2" color="text.secondary" mb={4}>
              Create your own team and view your team members and tools
            </Typography>

            <Box mb={3}>
              <Typography variant="body2" fontWeight={500} mb={1}>
                Team Name
              </Typography>
              <TextField
                fullWidth
                placeholder="Type your team name"
                value={teamName}
                onChange={(e) => setTeamName(e.target.value)}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    bgcolor: 'background.paper',
                  },
                }}
              />
            </Box>

            <Box mb={3}>
              <Typography variant="body2" fontWeight={500} mb={1}>
                Description
              </Typography>
              <TextField
                fullWidth
                multiline
                rows={3}
                placeholder="Type your team description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    bgcolor: 'background.paper',
                  },
                }}
              />
            </Box>

            <Box mb={4}>
              <Typography variant="body2" fontWeight={500} mb={2}>
                Flow Control
              </Typography>
              <FormControl>
                <RadioGroup
                  row
                  value={flowControl}
                  onChange={(e) => setFlowControl(e.target.value)}
                >
                  <FormControlLabel
                    value="auto"
                    control={<Radio size="small" />}
                    label="Auto"
                    sx={{ mr: 3 }}
                  />
                  <FormControlLabel
                    value="manual"
                    control={
                      <Radio
                        size="small"
                        sx={{
                          color: '#7c3aed',
                          '&.Mui-checked': {
                            color: '#7c3aed',
                          },
                        }}
                      />
                    }
                    label="Manual"
                  />
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>
        );
      default:
        return (
          <Box textAlign="center" py={4}>
            <Typography variant="h6" color="text.secondary">
              Step {step + 1} content will be implemented here
            </Typography>
          </Box>
        );
    }
  };

  return (
    <Box sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link
          color="text.secondary"
          onClick={() => router.push('/dashboard/teams')}
          sx={{ cursor: 'pointer', textDecoration: 'none' }}
        >
          Teams
        </Link>
        <Typography color="text.primary">Create Team</Typography>
      </Breadcrumbs>

      {/* Header */}
      <Typography variant="h4" fontWeight={700} mb={4}>
        Create Team
      </Typography>

      {/* Stepper */}
      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
        {steps.map((label, index) => (
          <Step key={label}>
            <StepLabel
              StepIconComponent={({ active, completed }) => (
                <Box
                  sx={{
                    width: 32,
                    height: 32,
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    bgcolor: active || completed ? '#7c3aed' : '#e5e7eb',
                    color: active || completed ? 'white' : '#6b7280',
                    fontWeight: 600,
                    fontSize: '0.875rem',
                  }}
                >
                  {completed ? <Icon icon="eva:checkmark-fill" width={16} /> : index + 1}
                </Box>
              )}
              sx={{
                '& .MuiStepLabel-label': {
                  fontWeight: activeStep === index ? 600 : 400,
                  color: activeStep === index ? 'text.primary' : 'text.secondary',
                },
              }}
            >
              {label}
            </StepLabel>
          </Step>
        ))}
      </Stepper>

      {/* Step Content */}
      <Box sx={{ mb: 4 }}>{renderStepContent(activeStep)}</Box>

      {/* Action Buttons */}
      <Box display="flex" gap={2}>
        <Button
          variant="outlined"
          onClick={handleCancel}
          sx={{
            flex: 1,
            borderColor: '#e5e7eb',
            color: '#6b7280',
            '&:hover': {
              borderColor: '#d1d5db',
              bgcolor: '#f9fafb',
            },
          }}
        >
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={activeStep === steps.length - 1 ? handleCancel : handleNext}
          sx={{
            flex: 1,
            bgcolor: '#7c3aed',
            '&:hover': {
              bgcolor: '#6d28d9',
            },
          }}
        >
          {activeStep === steps.length - 1 ? 'Create Team' : 'Next'}
        </Button>
      </Box>
    </Box>
  );
};

export default TeamsTemplateForm;
